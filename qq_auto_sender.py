#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QQ自动发消息脚本
功能：连续给指定联系人发送消息，一秒钟两条，持续10秒
"""

import time
import pyautogui
import tkinter as tk
from tkinter import messagebox, simpledialog
import threading

# 配置pyautogui
pyautogui.FAILSAFE = False  # 禁用故障保护
pyautogui.PAUSE = 0.1  # 设置操作间隔

class QQAutoSender:
    def __init__(self):
        self.is_running = False
        self.setup_gui()
        
    def setup_gui(self):
        """设置GUI界面"""
        self.root = tk.Tk()
        self.root.title("QQ自动发消息工具")
        self.root.geometry("400x300")
        
        # 标题
        title_label = tk.Label(self.root, text="QQ自动发消息工具", font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # 说明文本
        info_text = """
使用说明：
1. 先打开QQ并进入要发送消息的聊天窗口
2. 确保聊天窗口的输入框处于激活状态
3. 点击"开始发送"按钮
4. 程序将在3秒倒计时后开始发送
5. 发送规则：每秒2条消息，持续10秒，共20条消息
        """
        info_label = tk.Label(self.root, text=info_text, justify=tk.LEFT, font=("Arial", 10))
        info_label.pack(pady=10)
        
        # 消息内容输入框
        tk.Label(self.root, text="要发送的消息内容:", font=("Arial", 12)).pack()
        self.message_entry = tk.Entry(self.root, width=40, font=("Arial", 10))
        self.message_entry.pack(pady=5)
        self.message_entry.insert(0, "这是一条测试消息")
        
        # 按钮框架
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=20)
        
        # 开始按钮
        self.start_button = tk.Button(button_frame, text="开始发送", 
                                    command=self.start_sending, 
                                    bg="green", fg="white", 
                                    font=("Arial", 12, "bold"))
        self.start_button.pack(side=tk.LEFT, padx=10)
        
        # 停止按钮
        self.stop_button = tk.Button(button_frame, text="停止发送", 
                                   command=self.stop_sending, 
                                   bg="red", fg="white", 
                                   font=("Arial", 12, "bold"),
                                   state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=10)
        
        # 状态标签
        self.status_label = tk.Label(self.root, text="状态：就绪", font=("Arial", 10))
        self.status_label.pack(pady=10)
        
    def start_sending(self):
        """开始发送消息"""
        message = self.message_entry.get().strip()
        if not message:
            messagebox.showerror("错误", "请输入要发送的消息内容！")
            return
            
        # 确认对话框
        result = messagebox.askyesno("确认", 
                                   f"即将开始发送消息：\n\n'{message}'\n\n"
                                   "发送规则：每秒2条，持续10秒，共20条\n\n"
                                   "请确保QQ聊天窗口已打开并且输入框处于激活状态。\n"
                                   "确定要开始吗？")
        if not result:
            return
            
        self.is_running = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        
        # 在新线程中执行发送任务
        thread = threading.Thread(target=self.send_messages, args=(message,))
        thread.daemon = True
        thread.start()
        
    def stop_sending(self):
        """停止发送消息"""
        self.is_running = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.status_label.config(text="状态：已停止")
        
    def send_messages(self, message):
        """发送消息的主要逻辑"""
        try:
            # 3秒倒计时
            for i in range(3, 0, -1):
                if not self.is_running:
                    return
                self.status_label.config(text=f"状态：{i}秒后开始发送...")
                self.root.update()
                time.sleep(1)
            
            # 开始发送消息
            total_messages = 4  # 2秒 × 2条/秒 = 4条
            sent_count = 0
            
            start_time = time.time()
            
            for i in range(total_messages):
                if not self.is_running:
                    break
                    
                # 发送消息 - 使用剪贴板方法（最可靠）
                try:
                    import pyperclip
                    # 将消息复制到剪贴板
                    pyperclip.copy(message)
                    # 粘贴消息
                    pyautogui.hotkey('ctrl', 'v')
                    time.sleep(0.1)
                    # 发送消息
                    pyautogui.press('enter')
                    time.sleep(0.1)
                except Exception as e:
                    print(f"发送消息时出错: {e}")
                    # 备用方案：直接输入
                    try:
                        pyautogui.typewrite(message, interval=0.02)
                        time.sleep(0.1)
                        pyautogui.press('enter')
                    except Exception as e2:
                        print(f"备用方案也失败: {e2}")
                sent_count += 1
                
                # 更新状态
                elapsed_time = time.time() - start_time
                self.status_label.config(text=f"状态：已发送 {sent_count}/{total_messages} 条消息 ({elapsed_time:.1f}s)")
                self.root.update()
                
                # 控制发送频率：每秒2条，即每0.5秒发送一条
                time.sleep(0.5)
                
            # 发送完成
            if self.is_running:
                self.status_label.config(text=f"状态：发送完成！共发送 {sent_count} 条消息")
                messagebox.showinfo("完成", f"消息发送完成！\n共发送了 {sent_count} 条消息")
            
        except Exception as e:
            messagebox.showerror("错误", f"发送过程中出现错误：\n{str(e)}")
        finally:
            self.is_running = False
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
            
    def run(self):
        """运行程序"""
        self.root.mainloop()

def main():
    """主函数"""
    print("QQ自动发消息工具启动中...")
    print("注意：使用前请确保已安装 pyautogui 库")
    print("安装命令：pip install pyautogui")

    try:
        print("正在创建GUI界面...")
        app = QQAutoSender()
        print("GUI界面创建成功，正在显示...")
        app.run()
    except ImportError as e:
        print(f"导入错误：{e}")
        print("请先安装所需的库：pip install pyautogui")
        input("按回车键退出...")
    except Exception as e:
        print(f"程序运行错误：{e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    main()
