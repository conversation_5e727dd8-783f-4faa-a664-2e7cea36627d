#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版QQ自动发消息脚本
直接运行，无GUI界面
"""

import time
import pyautogui

# 配置pyautogui
pyautogui.FAILSAFE = False  # 禁用故障保护（鼠标移到屏幕角落不会中断）
pyautogui.PAUSE = 0.1  # 设置操作间隔

def send_qq_messages():
    """
    连续发送QQ消息
    规则：一秒钟两条消息，连续发10秒，共20条消息
    """
    
    # 要发送的消息内容（可以修改这里）
    message = "这是一条自动发送的消息"
    
    print("QQ自动发消息脚本")
    print("=" * 40)
    print(f"消息内容: {message}")
    print("发送规则: 每秒2条，持续10秒，共20条")
    print("=" * 40)
    
    # 给用户准备时间
    print("\n请确保：")
    print("1. QQ已打开并进入目标聊天窗口")
    print("2. 聊天输入框处于激活状态（光标在输入框内）")
    print("3. 准备好后，程序将在5秒后开始发送...")
    
    # 5秒倒计时
    for i in range(5, 0, -1):
        print(f"倒计时: {i} 秒...")
        time.sleep(1)
    
    print("\n开始发送消息...")
    
    # 发送消息
    total_messages = 20  # 10秒 × 2条/秒 = 20条
    start_time = time.time()
    
    try:
        for i in range(total_messages):
            # 输入消息并发送 - 使用剪贴板方法
            try:
                import pyperclip
                # 将消息复制到剪贴板
                pyperclip.copy(message)
                # 粘贴消息
                pyautogui.hotkey('ctrl', 'v')
                time.sleep(0.1)  # 等待粘贴完成
                # 发送消息
                pyautogui.press('enter')
                time.sleep(0.1)  # 等待发送完成
            except Exception as e:
                print(f"发送第 {i+1} 条消息时出错: {e}")
                # 备用方案：直接输入
                try:
                    pyautogui.typewrite(message, interval=0.02)
                    time.sleep(0.1)
                    pyautogui.press('enter')
                except Exception as e2:
                    print(f"备用方案也失败: {e2}")
                continue
            
            current_time = time.time()
            elapsed_time = current_time - start_time
            
            print(f"已发送第 {i+1} 条消息 (用时: {elapsed_time:.1f}s)")
            
            # 控制发送频率：每0.5秒发送一条（即每秒2条）
            time.sleep(0.5)
            
    except KeyboardInterrupt:
        print("\n\n用户中断发送！")
        return
    except Exception as e:
        print(f"\n发送过程中出现错误: {e}")
        return
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"\n发送完成！")
    print(f"总共发送: {total_messages} 条消息")
    print(f"总用时: {total_time:.1f} 秒")
    print(f"平均速度: {total_messages/total_time:.1f} 条/秒")

def main():
    """主函数"""
    try:
        send_qq_messages()
    except ImportError:
        print("错误：缺少 pyautogui 库")
        print("请运行以下命令安装：")
        print("pip install pyautogui")
    except Exception as e:
        print(f"程序运行错误: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
