#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装依赖库的脚本
"""

import subprocess
import sys

def install_package(package_name):
    """安装指定的包"""
    try:
        print(f"正在安装 {package_name}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✓ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {package_name} 安装失败: {e}")
        return False

def main():
    """主函数"""
    print("QQ自动发消息脚本 - 依赖安装程序")
    print("=" * 40)
    
    # 需要安装的包
    packages = ["pyautogui"]
    
    success_count = 0
    for package in packages:
        if install_package(package):
            success_count += 1
    
    print("\n" + "=" * 40)
    if success_count == len(packages):
        print("✓ 所有依赖安装完成！")
        print("现在可以运行 simple_qq_sender.py 或 qq_auto_sender.py")
    else:
        print(f"✗ 有 {len(packages) - success_count} 个包安装失败")
        print("请检查网络连接或手动安装")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
