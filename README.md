# QQ自动发消息脚本

这是一个用Python编写的QQ自动发消息工具，可以连续给指定联系人发送消息。

## 功能特点

- 一秒钟发送两条消息
- 连续发送10秒（共20条消息）
- 提供GUI版本和命令行版本
- 支持自定义消息内容
- 安全的倒计时机制

## 文件说明

1. **simple_qq_sender.py** - 简化版命令行脚本（推荐新手使用）
2. **qq_auto_sender.py** - 完整版GUI界面脚本
3. **install_requirements.py** - 依赖安装脚本
4. **README.md** - 使用说明文档

## 安装依赖

### 方法1：使用安装脚本
```bash
python install_requirements.py
```

### 方法2：手动安装
```bash
pip install pyautogui
```

## 使用方法

### 简化版（推荐）

1. 打开QQ，进入要发送消息的聊天窗口
2. 确保聊天输入框处于激活状态（光标在输入框内）
3. 运行脚本：
   ```bash
   python simple_qq_sender.py
   ```
4. 按照提示操作，程序会在5秒倒计时后开始发送

### GUI版本

1. 运行脚本：
   ```bash
   python qq_auto_sender.py
   ```
2. 在界面中输入要发送的消息内容
3. 点击"开始发送"按钮
4. 按照提示操作

## 注意事项

⚠️ **重要提醒**：
- 请合理使用此工具，避免对他人造成骚扰
- 使用前确保QQ聊天窗口已打开并激活
- 发送过程中可以按Ctrl+C中断（命令行版本）
- 建议先用少量消息测试功能

## 自定义设置

### 修改消息内容
编辑 `simple_qq_sender.py` 文件中的这一行：
```python
message = "这是一条自动发送的消息"  # 修改这里的内容
```

### 修改发送参数
如需修改发送频率或持续时间，可以调整以下参数：
```python
total_messages = 20  # 总消息数量
time.sleep(0.5)      # 发送间隔（秒）
```

## 故障排除

### 常见问题

1. **ImportError: No module named 'pyautogui'**
   - 解决方案：运行 `python install_requirements.py` 或 `pip install pyautogui`

2. **消息发送到错误的窗口**
   - 解决方案：确保QQ聊天窗口处于前台并且输入框已激活

3. **发送速度不准确**
   - 解决方案：关闭其他占用CPU的程序，确保系统响应及时

## 技术原理

脚本使用 `pyautogui` 库模拟键盘输入：
- `pyautogui.typewrite()` - 模拟打字输入
- `pyautogui.press('enter')` - 模拟按下回车键
- `time.sleep()` - 控制发送间隔

## 免责声明

此工具仅供学习和测试使用。使用者应当：
- 遵守相关法律法规
- 尊重他人权益，避免骚扰行为
- 承担使用此工具的一切后果

作者不对使用此工具造成的任何问题承担责任。
