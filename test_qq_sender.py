#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QQ发送测试脚本 - 用于诊断问题
"""

import time
import pyautogui

# 配置pyautogui
pyautogui.FAILSAFE = False  # 禁用故障保护
pyautogui.PAUSE = 0.1  # 设置操作间隔

def test_basic_functions():
    """测试基本功能"""
    print("=== PyAutoGUI 基本功能测试 ===")
    
    # 测试1：获取屏幕尺寸
    screen_size = pyautogui.size()
    print(f"屏幕尺寸: {screen_size}")
    
    # 测试2：获取鼠标位置
    mouse_pos = pyautogui.position()
    print(f"当前鼠标位置: {mouse_pos}")
    
    # 测试3：测试键盘输入
    print("\n准备测试键盘输入...")
    print("请在5秒内点击任意文本输入框（如记事本、QQ聊天框等）")
    
    for i in range(5, 0, -1):
        print(f"倒计时: {i} 秒...")
        time.sleep(1)
    
    print("\n开始测试输入...")
    
    # 测试输入
    test_message = "测试消息123"
    try:
        pyautogui.typewrite(test_message, interval=0.05)
        print(f"✓ 成功输入: {test_message}")
        
        time.sleep(0.5)
        
        # 测试回车键
        pyautogui.press('enter')
        print("✓ 成功按下回车键")
        
    except Exception as e:
        print(f"✗ 输入测试失败: {e}")
    
    print("\n=== 测试完成 ===")

def test_qq_sending():
    """专门测试QQ发送"""
    print("\n=== QQ发送专项测试 ===")
    
    message = "这是一条测试消息"
    
    print("请确保：")
    print("1. QQ已打开并进入聊天窗口")
    print("2. 聊天输入框处于激活状态（光标在输入框内）")
    print("3. 准备好后按回车继续...")
    
    input()  # 等待用户确认
    
    print("将在3秒后开始发送测试消息...")
    for i in range(3, 0, -1):
        print(f"倒计时: {i} 秒...")
        time.sleep(1)
    
    print("开始发送...")
    
    try:
        # 方法1：直接输入
        print("方法1：使用 typewrite 输入...")
        pyautogui.typewrite(message, interval=0.02)
        time.sleep(0.2)
        pyautogui.press('enter')
        print("✓ 方法1完成")
        
        time.sleep(1)
        
        # 方法2：逐字符输入
        print("方法2：逐字符输入...")
        for char in message:
            pyautogui.typewrite(char)
            time.sleep(0.05)
        time.sleep(0.2)
        pyautogui.press('enter')
        print("✓ 方法2完成")
        
        time.sleep(1)
        
        # 方法3：使用剪贴板
        print("方法3：使用剪贴板...")
        try:
            import pyperclip
            pyperclip.copy(message)
            pyautogui.hotkey('ctrl', 'v')
            time.sleep(0.2)
            pyautogui.press('enter')
            print("✓ 方法3完成")
        except ImportError:
            print("✗ 方法3失败：缺少 pyperclip 库")
        
    except Exception as e:
        print(f"✗ 发送测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("QQ发送功能诊断工具")
    print("=" * 40)
    
    while True:
        print("\n请选择测试项目：")
        print("1. 基本功能测试")
        print("2. QQ发送专项测试")
        print("3. 退出")
        
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == '1':
            test_basic_functions()
        elif choice == '2':
            test_qq_sending()
        elif choice == '3':
            print("退出程序")
            break
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
